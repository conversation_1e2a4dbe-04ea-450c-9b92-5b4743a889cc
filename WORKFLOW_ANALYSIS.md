# Phân Tích Chi Tiết Luồng Làm Việc - Background Generator

## 🎯 Tổng Quan Luồng Làm Vi<PERSON> bạn upload hình ảnh và bấm "Xóa Background", hệ thống sẽ thực hiện theo luồng sau:

```
1. Upload Image → 2. Remove Background → 3. Upscale → 4. Generate Background → 5. Download
```

## 📁 Cấu Trúc Thư Mục Quan Trọng

```
src/
├── app/
│   ├── upload/page.tsx                    # Trang chính upload
│   └── api/
│       ├── upload/route.ts                # API upload hình ảnh
│       └── process/
│           ├── remove-background/route.ts # API xóa background
│           ├── upscale/route.ts          # API tăng độ phân giải
│           └── generate-background/route.ts # API tạo background mới
├── components/
│   └── upload/
│       ├── step-by-step-processor.tsx    # Component xử lý từng bước
│       └── drag-drop-zone.tsx           # Component kéo thả file
├── lib/
│   ├── supabase.ts                      # Kết nối database & storage
│   └── runware.ts                       # Kết nối Runware API
└── types/index.ts                       # Đ<PERSON>nh nghĩa types
```

## 🔄 Chi Tiết Luồng Làm Việc

### BƯỚC 1: Upload Hình Ảnh

#### Frontend (`src/app/upload/page.tsx`)
```typescript
// User chọn file hoặc nhập URL
const handleFileSelect = (file: File) => {
  setSelectedFile({
    id: Date.now().toString(),
    file,
    preview: URL.createObjectURL(file),
    name: file.name,
    size: file.size,
    type: file.type,
  });
};
```

#### Component Processor (`src/components/upload/step-by-step-processor.tsx`)
```typescript
const handleUpload = async () => {
  // Tạo FormData
  const formData = new FormData();
  if (selectedFile) {
    formData.append("file", selectedFile);
  } else if (selectedUrl) {
    formData.append("url", selectedUrl);
  }

  // Gọi API upload
  const response = await fetch("/api/upload", {
    method: "POST",
    body: formData,
  });

  const result = await response.json();
  // Lưu imageId để sử dụng cho các bước tiếp theo
  setWorkflow(prev => ({
    ...prev,
    currentImageId: result.data.imageId
  }));
};
```

#### Backend API (`src/app/api/upload/route.ts`)
```typescript
export async function POST(request: NextRequest) {
  // 1. Kiểm tra authentication
  const { data: { user }, error: authError } = await supabase.auth.getUser();
  if (!user) return 401;

  // 2. Parse form data
  const formData = await request.formData();
  const file = formData.get("file") as File;

  // 3. Tạo tên file unique
  const uniqueFileName = `original/${timestamp}-${randomId}.${extension}`;

  // 4. Upload lên Supabase Storage
  const { data: uploadData, error } = await supabase.storage
    .from("images")
    .upload(uniqueFileName, file);

  // 5. Lưu record vào database
  const { data: imageRecord } = await supabase
    .from('processed_images')
    .insert({
      original_url: publicUrl,
      status: "uploaded",
    });

  // 6. Trả về imageId và URL
  return { imageId: imageRecord.id, publicUrl };
}
```

#### Database (`processed_images` table)
```sql
-- Record được tạo với status = "uploaded"
INSERT INTO processed_images (
  id,                    -- UUID tự động tạo
  original_url,          -- URL của file đã upload
  status,                -- "uploaded"
  created_at,            -- Timestamp hiện tại
  updated_at             -- Timestamp hiện tại
);
```

### BƯỚC 2: Xóa Background (KHI BẠN BẤM NÚT)

#### Frontend Click Handler (`src/components/upload/step-by-step-processor.tsx`)
```typescript
const handleRemoveBackground = async () => {
  // Kiểm tra có imageId không
  if (!workflow.currentImageId) return;

  // Cập nhật UI state thành "processing"
  updateStep('removeBackground', {
    status: 'processing',
    error: undefined
  });

  try {
    // Gọi API remove background
    const response = await fetch("/api/process/remove-background", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({
        imageId: workflow.currentImageId
      }),
    });

    if (!response.ok) {
      throw new Error("Background removal failed");
    }

    const result = await response.json();

    // Cập nhật UI state thành "completed"
    updateStep('removeBackground', {
      status: 'completed',
      result: result.data.backgroundRemovedUrl
    });

    // Hiển thị toast thành công
    toast({
      title: "Thành công",
      description: "Đã xóa background thành công",
    });

  } catch (error) {
    // Cập nhật UI state thành "error"
    updateStep('removeBackground', {
      status: 'error',
      error: error.message
    });
  }
};
```

#### Backend API (`src/app/api/process/remove-background/route.ts`)
```typescript
export async function POST(request: NextRequest) {
  console.log(`[API] POST /api/process/remove-background - Request received`);

  try {
    // 1. Parse request body
    const { imageId } = await request.json();

    if (!imageId) {
      return NextResponse.json({
        success: false,
        error: "Image ID is required"
      }, { status: 400 });
    }

    // 2. Lấy thông tin image từ database
    const imageRecord = await getProcessedImage(imageId);

    if (!imageRecord) {
      return NextResponse.json({
        success: false,
        error: "Image not found"
      }, { status: 404 });
    }

    // 3. Cập nhật status thành "processing_background_removal"
    await updateProcessedImage(imageId, {
      status: "processing_background_removal",
    });

    // 4. Gọi Runware API để xóa background
    const backgroundRemovedUrl = await removeBackground(imageRecord.original_url);

    // 5. Cập nhật database với kết quả
    const updatedRecord = await updateProcessedImage(imageId, {
      background_removed_url: backgroundRemovedUrl,
      status: "background_removed",
    });

    // 6. Trả về kết quả thành công
    return NextResponse.json({
      success: true,
      data: {
        imageId: updatedRecord.id,
        backgroundRemovedUrl: backgroundRemovedUrl,
        status: updatedRecord.status,
      },
      message: "Background removed successfully",
    });

  } catch (error) {
    // Cập nhật status thành "error" nếu có lỗi
    await updateProcessedImage(imageId, {
      status: "error",
    });

    return NextResponse.json({
      success: false,
      error: error.message,
    }, { status: 500 });
  }
}
```

#### Runware API Client (`src/lib/runware.ts`)
```typescript
async removeBackground(imageUrl: string): Promise<string> {
  // Tạo UUID unique cho task
  const taskUUID = crypto.randomUUID();

  // Tạo task object theo đúng format Runware API
  const task: RunwareTask = {
    taskType: 'imageBackgroundRemoval',  // ✅ Đã sửa từ 'removeBackground'
    taskUUID: taskUUID,                  // ✅ Đã thêm
    inputImage: imageUrl,
    outputFormat: 'PNG',
    outputType: 'URL',
    model: 'runware:109@1',             // ✅ Đã thêm
  };

  // Gọi Runware API
  const results = await this.makeRequest([task]);
  const result = results[0];

  if (result.error) {
    throw new Error(`Background removal failed: ${result.error}`);
  }

  if (!result.imageURL) {
    throw new Error('No image URL returned from background removal');
  }

  return result.imageURL;
}
```

#### Database Update
```sql
-- Lần 1: Cập nhật status thành "processing_background_removal"
UPDATE processed_images
SET
  status = 'processing_background_removal',
  updated_at = NOW()
WHERE id = 'imageId';

-- Lần 2: Cập nhật với kết quả thành công
UPDATE processed_images
SET
  background_removed_url = 'https://runware-result-url.com/image.png',
  status = 'background_removed',
  updated_at = NOW()
WHERE id = 'imageId';

-- Hoặc nếu có lỗi:
UPDATE processed_images
SET
  status = 'error',
  updated_at = NOW()
WHERE id = 'imageId';
```

## 🔍 Các Điểm Có Thể Gây Lỗi

### 1. Authentication Issues
- **Middleware** (`src/middleware.ts`): Kiểm tra user authentication
- **API Routes**: Tất cả đều yêu cầu authentication
- **Lỗi có thể**: 401 Unauthorized nếu không đăng nhập

### 2. Database Connection Issues
- **Supabase Client** (`src/lib/supabase.ts`): Kết nối database
- **Environment Variables**: Cần đúng SUPABASE_URL và ANON_KEY
- **Lỗi có thể**: Database connection failed

### 3. Runware API Issues
- **API Key**: Cần đúng RUNWARE_API_KEY
- **Task Parameters**: Phải đúng format (đã sửa trong lần này)
- **Lỗi có thể**: 404, 400, 500 từ Runware API

### 4. File Storage Issues
- **Supabase Storage**: Bucket "images" phải tồn tại
- **Permissions**: Bucket phải có quyền public read
- **Lỗi có thể**: Upload failed, file not accessible

## 🚨 Các Lỗi Thường Gặp và Cách Debug

### Lỗi 404 - Image Not Found
```typescript
// Kiểm tra trong database
const imageRecord = await getProcessedImage(imageId);
if (!imageRecord) {
  // Lỗi: imageId không tồn tại trong database
  // Nguyên nhân: Upload không thành công hoặc imageId sai
}
```

### Lỗi 401 - Authentication Required
```typescript
// Kiểm tra authentication
const { data: { user }, error } = await supabase.auth.getUser();
if (!user) {
  // Lỗi: User chưa đăng nhập
  // Giải pháp: Redirect đến /auth/login
}
```

### Lỗi Runware API
```typescript
// Kiểm tra response từ Runware
if (result.error) {
  // Lỗi từ Runware API
  // Có thể do: sai parameters, hết quota, API key sai
}
```

## 📊 State Management

### Frontend State (`step-by-step-processor.tsx`)
```typescript
interface WorkflowState {
  currentImageId?: string;           // ID của image đã upload
  upload: StepState;                 // Trạng thái upload
  removeBackground: StepState;       // Trạng thái xóa background
  upscale: StepState;               // Trạng thái upscale
  generateBackground: StepState;     // Trạng thái tạo background
  finalize: StepState;              // Trạng thái hoàn thành
}

interface StepState {
  status: 'idle' | 'processing' | 'completed' | 'error';
  result?: string;                  // URL kết quả
  error?: string;                   // Thông báo lỗi
}
```

### Database State (`processed_images` table)
```sql
-- Các trạng thái có thể:
status = 'uploaded'                    -- Vừa upload xong
status = 'processing_background_removal' -- Đang xóa background
status = 'background_removed'          -- Đã xóa background xong
status = 'processing_upscale'          -- Đang upscale
status = 'upscaled'                    -- Đã upscale xong
status = 'processing_background_generation' -- Đang tạo background
status = 'completed'                   -- Hoàn thành tất cả
status = 'error'                       -- Có lỗi xảy ra
```

## 🔧 Cách Debug Hiệu Quả

### 1. Kiểm tra Browser Console
```javascript
// Mở F12 → Console tab
// Xem logs từ frontend
console.log('[Frontend] Starting background removal...');
```

### 2. Kiểm tra Server Logs
```bash
# Terminal đang chạy npm run dev
[API] POST /api/process/remove-background - Request received
[API] Request body: { imageId: "..." }
[Runware] Starting background removal for image: ...
```

### 3. Kiểm tra Database
```sql
-- Truy vấn trạng thái hiện tại
SELECT id, status, original_url, background_removed_url, created_at
FROM processed_images
ORDER BY created_at DESC
LIMIT 5;
```

### 4. Kiểm tra Network Tab
```
F12 → Network tab → Xem các request:
- POST /api/upload
- POST /api/process/remove-background
- Kiểm tra status code và response
```

## 🎯 Điểm Cần Kiểm Tra Khi Có Lỗi

1. **User đã đăng nhập chưa?** → Kiểm tra `/auth/login`
2. **ImageId có tồn tại không?** → Kiểm tra database
3. **Runware API key đúng chưa?** → Kiểm tra `.env.local`
4. **Supabase connection OK không?** → Kiểm tra environment variables
5. **File đã upload thành công chưa?** → Kiểm tra Supabase Storage
6. **Logs có thông tin gì?** → Kiểm tra browser console và server terminal

## 📝 Checklist Debug

- [ ] User đã đăng nhập
- [ ] Environment variables đã cấu hình đúng
- [ ] Database connection hoạt động
- [ ] Supabase Storage bucket "images" tồn tại
- [ ] Runware API key hợp lệ
- [ ] Image đã upload thành công vào database
- [ ] Logs chi tiết đã được bật
- [ ] Network requests trả về status code gì
