import { RunwareTask, RunwareResponse } from '@/types';

const RUNWARE_API_KEY = process.env.RUNWARE_API_KEY!;
const RUNWARE_API_URL = process.env.RUNWARE_API_URL || 'https://api.runware.ai';

class RunwareClient {
  private apiKey: string;
  private baseUrl: string;

  constructor(apiKey: string, baseUrl: string) {
    this.apiKey = apiKey;
    this.baseUrl = baseUrl;
  }

  private async makeRequest(tasks: RunwareTask[]): Promise<RunwareResponse[]> {
    console.log(`[Runware] Making request to ${this.baseUrl}/v1`);
    console.log(`[Runware] Request payload:`, JSON.stringify(tasks, null, 2));

    const response = await fetch(`${this.baseUrl}/v1`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.apiKey}`,
      },
      body: JSON.stringify(tasks),
    });

    console.log(`[Runware] Response status: ${response.status} ${response.statusText}`);

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`[Runware] Error response:`, errorText);
      throw new Error(`Runware API error: ${response.status} ${response.statusText} - ${errorText}`);
    }

    const data = await response.json();
    console.log(`[Runware] Response data:`, JSON.stringify(data, null, 2));

    // Runware API returns data in { data: [...] } format
    if (data.data && Array.isArray(data.data)) {
      return data.data;
    }

    // If there's an error in the response
    if (data.errors && Array.isArray(data.errors)) {
      const errorMessage = data.errors.map((err: any) => err.message).join(', ');
      throw new Error(`Runware API error: ${errorMessage}`);
    }

    // Fallback - return as is if structure is unexpected
    return Array.isArray(data) ? data : [data];
  }

  async removeBackground(imageUrl: string): Promise<string> {
    // Generate unique UUID for this task
    const taskUUID = crypto.randomUUID();

    console.log(`[Runware] Starting background removal for image: ${imageUrl}`);
    console.log(`[Runware] Task UUID: ${taskUUID}`);

    const task: RunwareTask = {
      taskType: 'imageBackgroundRemoval',
      taskUUID: taskUUID,
      inputImage: imageUrl,
      outputFormat: 'PNG',
      outputType: 'URL',
      model: 'runware:109@1', // RemBG 1.4 model
    };

    console.log(`[Runware] Sending request with task:`, JSON.stringify(task, null, 2));

    try {
      const results = await this.makeRequest([task]);
      console.log(`[Runware] Received response:`, JSON.stringify(results, null, 2));

      if (!results || results.length === 0) {
        throw new Error('No results returned from Runware API');
      }

      const result = results[0];

      // Check for errors in the result
      if (result.error) {
        console.error(`[Runware] API returned error:`, result.error);
        throw new Error(`Background removal failed: ${result.error}`);
      }

      // Check for imageURL in the response
      if (!result.imageURL) {
        console.error(`[Runware] No imageURL in response:`, result);
        console.error(`[Runware] Available properties:`, Object.keys(result));
        throw new Error('No image URL returned from background removal');
      }

      console.log(`[Runware] Background removal successful. Result URL: ${result.imageURL}`);
      return result.imageURL;
    } catch (error) {
      console.error(`[Runware] Background removal error:`, error);

      // Re-throw with more context if it's our error
      if (error instanceof Error) {
        throw new Error(`Runware background removal failed: ${error.message}`);
      }

      throw new Error('Runware background removal failed: Unknown error');
    }
  }

  async upscaleImage(imageUrl: string, scale: number = 2): Promise<string> {
    const task: RunwareTask = {
      taskType: 'upscale',
      inputImage: imageUrl,
      outputFormat: 'PNG',
      outputType: 'URL',
      // Add upscale specific parameters
      ...{
        upscaleFactor: scale,
        model: 'RealESRGAN_x2plus'
      }
    };

    const results = await this.makeRequest([task]);
    const result = results[0];

    if (result.error) {
      throw new Error(`Image upscaling failed: ${result.error}`);
    }

    if (!result.imageURL) {
      throw new Error('No image URL returned from upscaling');
    }

    return result.imageURL;
  }

  async generateBackground(
    productImageUrl: string,
    prompt: string = "professional product photography background, clean, modern, high quality"
  ): Promise<string> {
    const task: RunwareTask = {
      taskType: 'imageInference',
      inputImage: productImageUrl,
      outputFormat: 'PNG',
      outputType: 'URL',
      // Add background generation specific parameters
      ...{
        positivePrompt: prompt,
        negativePrompt: "blurry, low quality, distorted, ugly, bad anatomy, extra limbs",
        model: "runware:100@1",
        steps: 25,
        CFGScale: 7,
        seed: Math.floor(Math.random() * 1000000),
        width: 1024,
        height: 1024,
        scheduler: "DPM++ 2M Karras",
        lora: [
          {
            model: "product_photography_lora",
            weight: 0.8
          }
        ]
      }
    };

    const results = await this.makeRequest([task]);
    const result = results[0];

    if (result.error) {
      throw new Error(`Background generation failed: ${result.error}`);
    }

    if (!result.imageURL) {
      throw new Error('No image URL returned from background generation');
    }

    return result.imageURL;
  }

  async enhanceImage(
    imageUrl: string,
    prompt: string = "high quality, sharp details, professional photography, 8k resolution"
  ): Promise<string> {
    const task: RunwareTask = {
      taskType: 'imageInference',
      inputImage: imageUrl,
      outputFormat: 'PNG',
      outputType: 'URL',
      // Add enhancement specific parameters
      ...{
        positivePrompt: prompt,
        negativePrompt: "blurry, low quality, noise, artifacts, distorted",
        model: "runware:100@1",
        steps: 30,
        CFGScale: 7.5,
        seed: Math.floor(Math.random() * 1000000),
        width: 1024,
        height: 1024,
        scheduler: "DPM++ 2M Karras",
        strength: 0.3, // Lower strength to preserve original image
        lora: [
          {
            model: "detail_enhancement_lora",
            weight: 0.6
          }
        ]
      }
    };

    const results = await this.makeRequest([task]);
    const result = results[0];

    if (result.error) {
      throw new Error(`Image enhancement failed: ${result.error}`);
    }

    if (!result.imageURL) {
      throw new Error('No image URL returned from enhancement');
    }

    return result.imageURL;
  }
}

// Export singleton instance
export const runwareClient = new RunwareClient(RUNWARE_API_KEY, RUNWARE_API_URL);

// Export individual functions for easier use
export const removeBackground = (imageUrl: string) => runwareClient.removeBackground(imageUrl);
export const upscaleImage = (imageUrl: string, scale?: number) => runwareClient.upscaleImage(imageUrl, scale);
export const generateBackground = (productImageUrl: string, prompt?: string) =>
  runwareClient.generateBackground(productImageUrl, prompt);
export const enhanceImage = (imageUrl: string, prompt?: string) =>
  runwareClient.enhanceImage(imageUrl, prompt);
